<?php

namespace Theme\Xmetr\Http\Controllers;

use Xmetr\Base\Enums\BaseStatusEnum;
use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Base\Http\Responses\BaseHttpResponse;
use Xmetr\Location\Http\Resources\CityResource;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\Country;
use Xmetr\Location\Repositories\Interfaces\CityInterface;
use Xmetr\RealEstate\Enums\PropertyTypeEnum;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Models\Currency;
use Xmetr\RealEstate\Models\Project;
use Xmetr\RealEstate\Repositories\Interfaces\ProjectInterface;
use Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface;
use Xmetr\SeoHelper\Facades\SeoHelper;
use Xmetr\Theme\Facades\Theme;
use Xmetr\Theme\Http\Controllers\PublicController;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\Rule;
use Theme\Xmetr\Actions\GetPropertiesAction;
use Theme\Xmetr\Http\Resources\ProjectResource;
use Theme\Xmetr\Http\Resources\PropertyResource;
use Theme\Xmetr\Services\TelegramBotService;
use Xmetr\Language\Facades\Language;

class XmetrController extends PublicController
{
    public function ajaxGetProperties(Request $request, GetPropertiesAction $getPropertiesAction): BaseHttpResponse
    {
        $request->validate([
            'type' => ['nullable', Rule::in(PropertyTypeEnum::values())],
            'limit' => ['required', 'integer'],
            'is_featured' => ['boolean'],
            'category_id' => ['nullable', 'string'],
        ]);

        $properties = $getPropertiesAction->handle(
            $request->integer('limit', 6),
            $request->input('category_id'),
            $request->string('type'),
            $request->boolean('is_featured')
        );

        return $this
            ->httpResponse()
            ->setData(view(
                Theme::getThemeNamespace('views.real-estate.properties.index'),
                ['properties' => $properties, 'itemsPerRow' => 3]
            )->render());
    }

    public function ajaxGetPropertiesForMap(Request $request): JsonResource
    {
        $validated = $request->validate([
            'k' => ['nullable', 'string'],
            'type' => ['nullable', Rule::in(PropertyTypeEnum::values())],
            'bedroom' => ['nullable', 'integer'],
            'bathroom' => ['nullable', 'integer'],
            'floor' => ['nullable', 'integer'],
            'min_price' => ['nullable', 'numeric'],
            'max_price' => ['nullable', 'numeric'],
            'min_square' => ['nullable', 'numeric'],
            'max_square' => ['nullable', 'numeric'],
            'project' => ['nullable', 'string'],
            'category' => ['nullable', 'array'],
            'category.*' => ['string'],
            'category_id' => ['nullable', 'string'],
            'city' => ['nullable', 'string'],
            'city_id' => ['nullable', 'integer'],
            'country_id' => ['nullable', 'integer'],
            'location' => ['nullable', 'string'],
            'no_fee' => ['nullable', 'string'],
        ]);

        $params = [
            'with' => array_merge(RealEstateHelper::getPropertyRelationsQuery(), ['author']),
            'paginate' => [
                'per_page' => 20,
                'current_paged' => $request->integer('page', 1),
            ],
        ];

        $properties = app(PropertyInterface::class)->getProperties($validated, $params);

        return $this
            ->httpResponse()
            ->setData(PropertyResource::collection($properties))
            ->toApiResponse();
    }

    public function ajaxSearchProjects(Request $request): BaseHttpResponse
    {
        $request->validate([
            'k' => ['nullable', 'string'],
        ]);

        $projects = Project::query()
            ->when($request->filled('k'), function (Builder $query) use ($request): void {
                $query->where('name', 'LIKE', '%' . $request->input('k') . '%');
            })
            ->select(['id', 'name'])
            ->take(10)
            ->oldest('name')
            ->get();

        return $this
            ->httpResponse()
            ->setData(view(
                Theme::getThemeNamespace('views.real-estate.partials.filters.projects-suggestion'),
                compact('projects')
            )->render());
    }

    public function ajaxGetProjectsForMap(Request $request, ProjectInterface $projectRepository)
    {
        $validated = $request->validate([
            'k' => ['nullable', 'string'],
            'type' => ['nullable', Rule::in(PropertyTypeEnum::values())],
            'bedroom' => ['nullable', 'integer'],
            'bathroom' => ['nullable', 'integer'],
            'floor' => ['nullable', 'integer'],
            'min_price' => ['nullable', 'numeric'],
            'max_price' => ['nullable', 'numeric'],
            'min_square' => ['nullable', 'numeric'],
            'max_square' => ['nullable', 'numeric'],
            'project' => ['nullable', 'string'],
            'category_id' => ['nullable', 'string'],
            'city' => ['nullable', 'string'],
            'city_id' => ['nullable', 'integer'],
            'country_id' => ['nullable', 'integer'],
            'build_class' => ['nullable', 'string'],
            'location' => ['nullable', 'string'],
        ]);

        $params = [
            'with' => array_merge(RealEstateHelper::getProjectRelationsQuery(), ['author']),
            'paginate' => [
                'per_page' => 20,
                'current_paged' => $request->integer('page', 1),
            ],
        ];

        $projects = $projectRepository->getProjects($validated, $params);

        return $this
            ->httpResponse()
            ->setData(ProjectResource::collection($projects))
            ->toApiResponse();
    }

    public function ajaxGetCities(Request $request, CityInterface $cityRepository)
    {
        $request->validate([
            'location' => ['nullable', 'string'],
        ]);

        $cities = $cityRepository->filters($request->input('location'));

        return $this
            ->httpResponse()
            ->setData(
                view(
                    Theme::getThemeNamespace('views.real-estate.partials.filters.cities-suggestion'),
                    compact('cities')
                )->render()
            );
    }

    public function getWishlist(Request $request, PropertyInterface $propertyRepository, ProjectInterface $projectRepository)
    {
        abort_unless(RealEstateHelper::isEnabledWishlist(), 404);

        SeoHelper::setTitle(__('Wishlist'))
            ->setDescription(__('Wishlist'));

        $propertyWishlist = isset($_COOKIE['wishlist']) ? explode(',', $_COOKIE['wishlist']) : [];
        $propertyWishlist = array_filter($propertyWishlist);
        $projectWishlist = isset($_COOKIE['project_wishlist']) ? explode(',', $_COOKIE['project_wishlist']) : [];
        $projectWishlist = array_filter($projectWishlist);

        $properties = collect();
        $projects = collect();

        if (! empty($propertyWishlist)) {
            $properties = $propertyRepository->advancedGet([
                'condition' => [
                    ['re_properties.id', 'IN', $propertyWishlist],
                ],
                'order_by' => [
                    're_properties.id' => 'DESC',
                ],
                'paginate' => [
                    'per_page' => (int) theme_option('number_of_properties_per_page', 12),
                    'current_paged' => $request->integer('page', 1),
                ],
                'with' => RealEstateHelper::getPropertyRelationsQuery(),
            ]);
        }

        if (! empty($projectWishlist)) {
            $projects = $projectRepository->advancedGet([
                'condition' => [
                    ['re_projects.id', 'IN', $projectWishlist],
                ],
                'order_by' => [
                    're_projects.id' => 'DESC',
                ],
                'paginate' => [
                    'per_page' => (int) theme_option('number_of_properties_per_page', 12),
                    'current_paged' => $request->integer('page', 1),
                ],
                'with' => RealEstateHelper::getProjectRelationsQuery(),
            ]);
        }

        Theme::breadcrumb()->add(__('Wishlist'));

        return Theme::scope('real-estate.wishlist', compact('properties', 'projects'))->render();
    }

    public function switchLangCurrency(Request $request)
    {
        $currentUrl = $request->input('lang'); // Full URL
        $requestedCurrency = $request->input('currency');

        if (session('currency') != $requestedCurrency) {
           session(['currency' => $requestedCurrency]);
        }
        // Parse locale from URL
        $path = parse_url($currentUrl, PHP_URL_PATH);
        $segments = explode('/', trim($path, '/'));
        $locale = $segments[0] ?? null;
        $currentLocale = Language::getCurrentLocale();
        // If unsupported locale or missing, fallback
        if (!$locale || !Language::checkLocaleInSupportedLocales($locale)) {
            return redirect()->back();
        }
        // If locale is same, no need to switch language
        if ($locale === $currentLocale) {
            return redirect()->back();
        }

        // Redirect to localized URL
        $redirectUrl = Language::getLocalizedURL($locale, $currentUrl, [], true);
        return redirect()->to($redirectUrl);
    }


    public function getCitiesByCountry(Request $request)
    {
        $country_id = $request->input('country_id');

        if (! $country_id) {
            return null;
        }

        $country =  Country::query()->where([
            'id' => $country_id,
            'status' => BaseStatusEnum::PUBLISHED,
        ])->first();
        $cities = '<li class="x-home-selectCity_option" data-value="">
                          <p>' . __('All') . '</p>
                        </li>';
        foreach ($country->cities as $city) {
            $cities .= '<li class="x-home-selectCity_option" data-value="' . $city->id . '">
                          <p>' . $city->name . '</p>
                        </li>';
        }
        return $cities;
    }

    public function ajaxGetCitiesByCountry(Request $request)
    {
        $data = City::query()
            ->select(['id', 'name'])
            ->wherePublished()
            ->orderBy('order')
            ->orderBy('name');

        $stateId = $request->input('state_id');

        if ($stateId && $stateId != 'null') {
            $data = $data->where('state_id', $stateId);
        }

        $countryId = $request->input('country_id');

        if ($countryId && $countryId != 'null') {
            $data = $data->where('country_id', $countryId);
        }

        $keyword = BaseHelper::stringify($request->query('k'));

        if ($keyword) {
            $data = $data
                ->where('name', 'LIKE', '%' . $keyword . '%')
                ->paginate(10);
        } else {
            $data = $data->get();
        }

        if ($keyword) {
            return $this
                ->httpResponse()
                ->setData([CityResource::collection($data), 'total' => $data->total()]);
        }

        $data->prepend(new City(['id' => 0, 'name' => trans('plugins/location::city.select_city')]));

        return $this
            ->httpResponse()
            ->setData(CityResource::collection($data));
    }

    public function ajaxGetActiveListingCitiesByCountry(Request $request)
    {

        $countryId = $request->input('country_id');
        $cities = City::query()
            ->select(['cities.*'])
            ->join('re_properties', 'cities.id', '=', 're_properties.city_id')
            ->where('re_properties.country_id', $countryId)
            ->orderBy('cities.name')
            ->distinct()
            ->get();

        $cities->prepend(new City(['id' => 0, 'name' => __('Choose City')]));

        return $this
            ->httpResponse()
            ->setData(CityResource::collection($cities));
    }

    public function telegramBot(Request $request)
    {
        $botToken = "7063184659:AAEX2sSnAp0M9M-3dHwTu0mfR1lwSxJx90s";
        $bot = new TelegramBotService($botToken);
        $update = json_decode(file_get_contents('php://input'), true);
        $bot->handleUpdate($update);
    }
}
