@php
    Theme::set('breadcrumbEnabled', 'no');
    Theme::set('headerClass', 'no-line');

    Theme::asset()
        ->container('footer')
        ->add('location-js', 'vendor/core/plugins/location/js/location.js?v=1.0.0', ['jquery']);

    $title = SeoHelper::getTitleOnly();

    // Get current filter parameters for JavaScript
    $currentFilters = [
        'country_id' => request()->query('country_id'),
        'city_id' => request()->query('city_id'),
        'category_id' => request()->query('category_id'),
        'bedroom' => request()->query('bedroom'),
        'bathroom' => request()->query('bathroom'),
        'min_price' => request()->query('min_price'),
        'max_price' => request()->query('max_price'),
        'type' => request()->query('type'),
    ];

    // Remove null values
    $currentFilters = array_filter($currentFilters, function($value) {
        return $value !== null && $value !== '';
    });

    // Check for excluded filters
    $hasExtraFilters = request()->hasAny([
        'suitable',
        'account_type',
        'floor',
        'min_price',
        'max_price',
        'furnished',
        'smoking_allowed',
        'features',
        'spoken_languages',
    ]);
        $countryName = null;
        $cityName = null;
    // Only apply custom SEO title if no extra filters
    if (!$hasExtraFilters) {

        $categoryInput = request()->query('category');
        $category = null;
        if ($categoryInput) {
            // Handle both single category and multiple categories
            $firstCategory = is_array($categoryInput) ? $categoryInput[0] : $categoryInput;
            $category = get_property_category_by_slug($firstCategory);
        }
        $categoryName = $category ? $category->name : null;

        $bedrooms = request()->query('bedroom');



        // Start with category SEO if available
        if ($category) {
            $category_seo = $category->getMetadata('seo_meta', true);
            if ($category) {
                $categoryName = $category->name;
                $category_seo = $category->getMetadata('seo_meta', true);
                $category_seo_title = $category_seo['seo_title'] ?? $category->name;
            }
        }

        // Get country & city names
        if (request()->query('country_id')) {
            $countryName = get_country_name_by_id(request()->query('country_id'));
        }

        if (request()->query('city_id')) {
            $cityName = get_city_name_by_id(request()->query('city_id'));
        }

        // Only build custom title if there is at least country, city, category, or bedrooms
         if ($categoryName && !$countryName && !$cityName && !$bedrooms) {
            $title = $category_seo_title;
            SeoHelper::setTitle($title);

        } elseif ($countryName || $cityName || $categoryName || $bedrooms) {

            if ($categoryName && $bedrooms && $countryName && $cityName) {
                $template = 'Residential Complexes Catalog :bedrooms Bedrooms :category in :country, :city';
            } elseif ($categoryName && $bedrooms && $countryName) {
                $template = 'Residential Complexes Catalog :bedrooms Bedrooms :category in :country';
            } elseif ($bedrooms && $countryName && $cityName) {
                $template = 'Residential Complexes Catalog :bedrooms Bedrooms in :country, :city';
            } elseif ($bedrooms && $countryName) {
                $template = 'Residential Complexes Catalog :bedrooms Bedrooms in :country';
            } elseif ($categoryName && $countryName && $cityName) {
                $template = 'Residential Complexes Catalog :category in :country, :city';
            } elseif ($categoryName && $countryName) {
                $template = 'Residential Complexes Catalog :category in :country';
            } elseif ($countryName && $cityName) {
                $template = 'Residential Complexes Catalog in :country, :city';
            } elseif ($countryName) {
                $template = 'Residential Complexes Catalog in :country';
            } else {
                $template = 'Residential Complexes Catalog';
            }

            $finalTitle = __($template, [
                'category' => $categoryName,
                'bedrooms' => $bedrooms,
                'country' => $countryName,
                'city' => $cityName,
            ]);
            $title  = $finalTitle;
            SeoHelper::setTitle($finalTitle);
        }
    }
    else {
            SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
            $title = SeoHelper::getTitleOnly();
            SeoHelper::setTitle($title);
        }

    if ($projects->count() == 0) {
        $title = __('No projects found.');
        SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
    }

    if (request()->has('page')){
        SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
    }

    $filteredQuery = collect(request()->query())
        ->except('page')
        ->all();


@endphp


<div class="signup-modal">
    <div class="modal  x-modal-scrollOffset" id="modalFilterSettings" aria-hidden="true"
        aria-labelledby="modalFilterSettingsLabel" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">


            <input type="hidden" name="page" value="{{ BaseHelper::stringify(request()->integer('page')) }}" />
            <input type="hidden" name="layout" value="{{ BaseHelper::stringify(request()->input('layout')) }}" />

            <div class="modal-content">
                <form action="{{ $actionUrl }}" data-url="{{ $ajaxUrl }}" data-type="projects" method="get" class="filter-form">
                    @csrf
                    <div class="modal-header !px-[20px] !py-[15px]">
                        <h5 class="modal-title text-[20px] text-black font-bold" id="modalFilterSettingsLabel">
                            {{ __('Search by parameters') }}</h5>
                            <button type="button"
                                class="w-[50px] h-[50px] rounded-[10px] bg-[#212329] flex justify-center items-center hover:bg-[#3e424d]"
                                data-bs-dismiss="modal" aria-label="Close">
                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </button>
                    </div>
                    <div class="modal-body x-fixedParent_wrapper w-full rounded-[15px] bg-white p-[20px] flex flex-col gap-[32px] relative"
                        id="x-filtersWrapper">


                        <div class="flex flex-col gap-[32px]">


                            <div class="flex flex-col gap-[20px] w-full">
                                <h5 class="title !text-[15px]">{{ __('Build Class') }}</h5>

                                <div class="grid grid-cols-2 gap-[12px] max-[460px]:grid-cols-1">

                                    @php
                                        use Xmetr\RealEstate\Enums\ProjectBuildClassEnum;

                                        $buildClass = collect(ProjectBuildClassEnum::labels())->map(function ($label, $value) {
                                            return (object) [
                                                'id' => $value,
                                                'name' => $label,
                                            ];
                                        });
                                    @endphp

                                    @foreach ($buildClass as $buildClassItem)
                                        <label class="x-radio-toggle max-[460px]:px-[4px]">
                                            <input type="radio" name="build_class" value="{{ $buildClassItem->id }}"
                                            @checked($buildClassItem->id == request()->query('build_class')) />
                                            <p>{{ $buildClassItem->name }}</p>
                                        </label>
                                    @endforeach


                                </div>
                            </div>
                        </div>


                        <!-- Location -->
                        <div class="flex flex-col gap-[20px] w-full">
                            <h5 class="title !text-[15px]">{{ __('Location') }}</h5>

                            <div class="border border-[#DDDDDD] rounded-[10px] px-[8px] w-full h-[50px]">

                                <select class="x-select x-select-city w-full h-full" id="filter-district"
                                    data-filter="country" name="country_id" data-type="country" autocomplete="country">
                                    {{-- <select class="x-select x-select-city w-full h-full" id="filter-district" name="country_id" autocomplete="country" > --}}
                                    <option value="" selected>{{ __('Choose Country') }}</option>
                                    @foreach (get_all_countries_for_projects() as $country)
                                        <option value="{{ $country->id }}" data-slug="{{ $country->slug }}"  data-url="{{ $country->url }}" data-projects-url="{{ route('public.projects-by-country', $country->slug) }}" @selected(request()->query('country_id') == $country->id)>
                                            {{ $country->name }}
                                        </option>
                                    @endforeach

                                </select>
                            </div>

                            <div class="border border-[#DDDDDD] rounded-[10px] px-[8px] w-full h-[50px]" id="filter-city_wrapper" @if(request()->query('country_id')) @else style="display: none;" @endif>
                                {{-- <select class="x-select x-select-city w-full h-full" id="filter-district" name="city_id"  data-type="city"  data-url="{{ route('ajax.cities-by-state') }}"> --}}
                                <select class="x-select x-select-city w-full h-full" id="filter-district" name="city_id"
                                    data-filter="city" data-type="city"
                                    data-url="{{ route('public.ajax-active-listing-cities-by-country') }}">
                                    {{-- <select class="x-select x-select-city w-full h-full" id="filter-district" name="city_id"> --}}
                                    <option value="" selected>{{ __('Choose City') }}</option>


                                    @if (request()->query('country_id'))
                                        @foreach (get_all_cities_for_projects(request()->query('country_id')) as $city)
                                            <option value="{{ $city->id }}" data-slug="{{ $city->slug }}"
                                                data-url="{{ $city->url }}" @selected(request()->query('city_id') == $city->id)>
                                                {{ $city->name }}</option>
                                        @endforeach
                                    @else
                                        @if ($cityName)
                                            <option value="{{ request()->query('city_id') }}" selected>
                                                {{ $cityName }}</option>
                                        @endif
                                    @endif
                                </select>
                            </div>

                        </div>

                        <span class="block w-full h-[1px] bg-[#DDDDDD]"></span>


                        <div class="flex flex-col gap-[32px]">
                            <!-- Amenities -->
                            <div class="flex flex-col gap-[20px] w-full">
                                <h5 class="title !text-[15px]">{{ __('Amenities') }}</h5>

                                @php
                                    $features = \Xmetr\RealEstate\Models\Feature::query()->has('properties')->wherePublished()->get();
                                @endphp

                                <div id="feature-list" class="grid grid-cols-2 gap-[12px] max-[460px]:grid-cols-1 overflow-hidden transition-all duration-300">
                                    @foreach ($features as $index => $feature)
                                        <label class="x-checkbox-toggle max-[460px]:px-[4px] feature-item {{ $index >= 4 ? 'hidden' : '' }}">
                                            <input type="checkbox" name="features[]"
                                                value="{{ $feature->getKey() }}" @checked(in_array($feature->getKey(), request()->query('features', []))) />
                                            <p>{{ $feature->name }}</p>
                                        </label>
                                    @endforeach
                                </div>

                                @if ($features->count() > 4)
                                    <button type="button" id="toggle-feature-btn" class="text-black text-sm hover:underline w-fit">
                                        {{ __('Show More') }}
                                    </button>
                                @endif
                            </div>
                        </div>

                        {{-- JavaScript --}}
                        <script>
                            document.addEventListener('DOMContentLoaded', function () {
                                const toggleBtn = document.getElementById('toggle-feature-btn');
                                const featureItems = document.querySelectorAll('.feature-item');

                                if (!toggleBtn) return;

                                let expanded = false;

                                toggleBtn.addEventListener('click', function () {
                                    expanded = !expanded;

                                    featureItems.forEach((item, index) => {
                                        if (index >= 4) {
                                            item.classList.toggle('hidden', !expanded);
                                        }
                                    });

                                    toggleBtn.textContent = expanded ? '{{ __('Show Less') }}' : '{{ __('Show More') }}';
                                });
                            });
                        </script>



                        <div class="x-fixedParent_end invisible"></div>

                        <!-- Menu -->
                        <div class="fixed -left-[9px] bottom-0 p-[20px] flex justify-center gap-[10px] w-full"
                            id="modalFilterSettingsInner">
                            <div class="flex justify-between gap-[10px] max-w-[440px] w-full">
                                <a href="{{ $actionUrl }}"
                                    class="x-filtersReset text-center bg-[#FBF0EE] px-[20px] py-[12px] duration-200 hover:bg-[#DC6F5A] [&>p]:hover:text-white rounded-[10px] grow z-[3]">
                                    <p class="text-[#DC6F5A] text-[15px] duration-200 font-bold">{{ __('Clear All') }}
                                    </p>
                                </a>

                                <button data-bs-toggle="modal" type="submit"
                                    class="bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center gap-[10px] relative max-[1024px]:px-[15px] shrink-0 max-[768px]:h-[50px] grow justify-center z-[3]">
                                    <p class="text-white text-[15px] font-bold text-center">
                                    <span id="project-count-display-xmetr" style="display: none;">{{ __('Show') }} <span id="project-count-number-xmetr">0</span> {{ __('projects') }} </span>
                                    <span id="project-filter-btn-xmetr">{{ __('Search') }}</span>
                                    <span id="project-no-results-xmetr" style="display: none;">{{ __('No results found') }}</span>
                                    </p>
                                </button>
                            </div>

                            <div class="x-fixedParent_shadow absolute bottom-0 left-0 w-full h-[95px]"
                                style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));">
                            </div>
                        </div>
                    </div>
                </form>
            </div>

        </div>
    </div>

    <div class="hiddenbar-body-ovelay"></div>
</div>

<!-- Map add x-filter-map--active to activate -->
<div class="x-filter-map fixed top-[88px] max-[1024px]:top-[71px] left-0 w-screen h-screen bg-white z-[20]"
    id="x-filter-map">
    <div
        class="container flex flex-row justify-end pt-[20px] max-[1024px]:!px-[20px] max-[1024px]:m-0 max-[1024px]:max-w-none">
        <button type="button"
            class="x-filter-map_trigger w-[50px] h-[50px] rounded-[10px] bg-[#212329] flex justify-center items-center hover:bg-[#3e424d] z-[3]">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3"
                    stroke-linecap="round" stroke-linejoin="round" />
            </svg>
        </button>
    </div>

    <div class="h-full">
        <div class="absolute bottom-[88px] max-[1024px]:bottom-[71px] left-0 w-full p-[20px] flex justify-center">
            <a href="#modalFilterSettings" role="button" data-bs-toggle="modal"
                class="x-fixedParent_trigger bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center gap-[10px] relative max-[1024px]:px-[15px] shrink-0 max-[768px]:h-[50px] w-fit z-[3]">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M16.48 16.5368H19M2.68 10L1 10.044M2.68 10C2.68 11.3255 3.75451 12.4 5.08 12.4C6.40548 12.4 7.48 11.3255 7.48 10C7.48 8.6745 6.40548 7.6 5.08 7.6C3.75451 7.6 2.68 8.6745 2.68 10ZM8.169 10.0441H19M10.801 3.55124H1M19 3.55124H16.48M1 16.5368H10.801M15.88 16.6C15.88 17.9255 14.8055 19 13.48 19C12.1545 19 11.08 17.9255 11.08 16.6C11.08 15.2745 12.1545 14.2 13.48 14.2C14.8055 14.2 15.88 15.2745 15.88 16.6ZM15.88 3.4C15.88 4.72548 14.8055 5.8 13.48 5.8C12.1545 5.8 11.08 4.72548 11.08 3.4C11.08 2.07452 12.1545 1 13.48 1C14.8055 1 15.88 2.07452 15.88 3.4Z"
                        stroke="white" stroke-width="2" stroke-linecap="round" />
                </svg>

                <p class="text-white text-[15px] font-bold">{{ __('Filters') }}</p>


                <p
                    class="x-filtersToggled w-[20px] h-[20px] text-center text-white text-[11px] font-bold bg-[#DC6F5A] rounded-[32px] hidden">
                </p>
            </a>

            <div class="absolute bottom-0 left-0 w-full h-[95px]"
                style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
        </div>

        <div id="x-filters-map" class="w-full h-full absolute top-0 left-0">
            @include(Theme::getThemeNamespace('views.real-estate.partials.map'), ['mapUrl' => $mapUrl ?? route('public.ajax.projects.map')])
        </div>
    </div>
</div>

<div class="wrapper ovh py-[24px] pb-[40px]">
    <div class="container flex flex-col gap-[30px]">
        <div class="flex flex-col gap-[15px]">
            <div class="flex flex-col gap-[10px]">
                <h1 class="title" id="filter-page-title">
                    {{ $title }}
                </h1>

                <p class="text-[15px] text-[#717171]">{{ __('Found') }} – <span
                        id="total-record-found">{{ $projects instanceof \Illuminate\Pagination\LengthAwarePaginator ? $projects->total() : $projects->count() }}</span>
                    {{ __('projects') }}</p>
            </div>

            <div class="flex justify-between items-stretch gap-[16px]">
                <div class="flex items-stretch gap-[10px] w-full">

                    {{-- <div class="h-auto border border-[#DDDDDD] rounded-[10px] w-[200px] px-[10px] max-[768px]:w-full">
                  <select class="x-select w-full h-full" name="category_id">
                    <option value="" selected>Rent Apartment</option>
                    @php
                    $categories = get_property_categories([
                        'indent' => '↳',
                        'conditions' => ['status' => \Xmetr\Base\Enums\BaseStatusEnum::PUBLISHED],
                    ]);
                    @endphp
                    @foreach ($categories as $category)
                    <option value="{{ $category->getKey() }}" @if (request()->query('category_id') == $category->getKey()) selected @endif >{{ $category->name }}</option>
                    @endforeach

                  </select>
                </div>

                <div class="h-auto border border-[#DDDDDD] rounded-[10px] w-[200px] px-[10px] max-[768px]:w-full">
                  <select class="x-select x-select-city w-full h-full" name="country_id" data-type="country" autocomplete="country">
                          <option value="" selected>Choose Country</option>
                          @foreach (get_all_countries() as $country)
                          <option value="{{ $country->id }}" @selected(request()->query('country_id') == $country->getKey()) >{{ $country->name }}</option>
                          @endforeach
                  </select>
                </div>

                <div class="h-auto border border-[#DDDDDD] rounded-[10px] w-[200px] px-[10px] max-[768px]:w-full">
                  <select class="x-select x-select-city w-full h-full" name="city_id"  data-type="city"  data-url="{{ route('ajax.cities-by-state') }}">
                    <option value="city-default" selected disabled>Choose City</option>
                    @foreach (get_all_cities() as $city)
                        <option value="{{ $city->id }}" @selected(request()->query('city_id') == $city->getKey()) >{{ $city->name }}</option>
                    @endforeach
                  </select>
                </div> --}}

                    <a href="#modalFilterSettings" role="button" data-bs-toggle="modal"
                        class="x-fixedParent_trigger bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center gap-[10px] relative max-[1024px]:px-[15px] shrink-0 max-[768px]:h-[50px]">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M16.48 16.5368H19M2.68 10L1 10.044M2.68 10C2.68 11.3255 3.75451 12.4 5.08 12.4C6.40548 12.4 7.48 11.3255 7.48 10C7.48 8.6745 6.40548 7.6 5.08 7.6C3.75451 7.6 2.68 8.6745 2.68 10ZM8.169 10.0441H19M10.801 3.55124H1M19 3.55124H16.48M1 16.5368H10.801M15.88 16.6C15.88 17.9255 14.8055 19 13.48 19C12.1545 19 11.08 17.9255 11.08 16.6C11.08 15.2745 12.1545 14.2 13.48 14.2C14.8055 14.2 15.88 15.2745 15.88 16.6ZM15.88 3.4C15.88 4.72548 14.8055 5.8 13.48 5.8C12.1545 5.8 11.08 4.72548 11.08 3.4C11.08 2.07452 12.1545 1 13.48 1C14.8055 1 15.88 2.07452 15.88 3.4Z"
                                stroke="white" stroke-width="2" stroke-linecap="round" />
                        </svg>

                        <p class="text-white text-[15px] font-bold ">{{ __('Filters') }}</p>

                        <p
                            class="x-filtersToggled hidden w-[20px] h-[20px] text-center text-white text-[11px] font-bold bg-[#DC6F5A] rounded-[32px] max-[1024px]:absolute max-[1024px]:-top-[10px] max-[1024px]:-right-[10px] max-[1024px]:border-[3px] max-[1024px]:border-white max-[1024px]:w-[26px] max-[1024px]:h-[26px]">
                        </p>
                    </a>

                    <a href="{{ $actionUrl }}"
                        class="x-filtersReset bg-[#FBF0EE] px-[20px] py-[12px] duration-200 hover:bg-[#DC6F5A] [&>p]:hover:text-white rounded-[10px] reset-filter-btn"
                        @style(['display: none' => empty($filteredQuery)])>
                        <p class="text-[#DC6F5A] text-[15px] duration-200 font-bold">{{ __('Reset') }}</p>
                    </a>

                    {{-- <button class="x-filtersReset bg-[#FBF0EE] px-[20px] py-[12px] duration-200 hover:bg-[#DC6F5A] [&>p]:hover:text-white rounded-[10px] max-[768px]:hidden">
                  <p class="text-[#DC6F5A] text-[15px] duration-200 font-bold">{{ __('Reset') }}</p>
                </button> --}}
                </div>

                <button
                    class="x-filter-map_trigger shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[10px] px-[20px] h-auto max-[768px]:hidden">
                    <svg width="20" height="18" viewBox="0 0 20 18" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M6.72604 1.60466V13.4768M13.274 3.99746V16.2166M1 5.05669V13.9425C1 15.6777 2.23288 16.39 3.73059 15.5316L5.87671 14.3078C6.34246 14.043 7.11872 14.0156 7.60273 14.2622L12.3973 16.664C12.8813 16.9014 13.6575 16.8832 14.1233 16.6183L18.0776 14.3535C18.5799 14.0613 19 13.3489 19 12.7645V3.87861C19 2.14346 17.7671 1.43113 16.2694 2.28957L14.1233 3.51332C13.6575 3.77816 12.8813 3.80555 12.3973 3.55898L7.60273 1.16629C7.11872 0.928845 6.34246 0.94711 5.87671 1.21195L1.92237 3.47679C1.41096 3.76902 1 4.48135 1 5.05669Z"
                            stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>

                    <p class="text-white text-[15px]">{{ __('Map') }}</p>
                </button>
            </div>
        </div>

        <div class="position-relative" data-bb-toggle="data-listing">
            @include($itemsViewPath, compact('itemLayout'))
        </div>

    </div>


    <div class="fixed left-0 bottom-0 w-full hidden max-[768px]:flex gap-[10px] items-stretch py-[20px] px-[30px] z-[3]">
        <button
            class="x-filter-map_trigger shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[10px] px-[20px] h-auto z-[4] grow flex-1">
            <svg width="20" height="18" viewBox="0 0 20 18" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M6.72604 1.60466V13.4768M13.274 3.99746V16.2166M1 5.05669V13.9425C1 15.6777 2.23288 16.39 3.73059 15.5316L5.87671 14.3078C6.34246 14.043 7.11872 14.0156 7.60273 14.2622L12.3973 16.664C12.8813 16.9014 13.6575 16.8832 14.1233 16.6183L18.0776 14.3535C18.5799 14.0613 19 13.3489 19 12.7645V3.87861C19 2.14346 17.7671 1.43113 16.2694 2.28957L14.1233 3.51332C13.6575 3.77816 12.8813 3.80555 12.3973 3.55898L7.60273 1.16629C7.11872 0.928845 6.34246 0.94711 5.87671 1.21195L1.92237 3.47679C1.41096 3.76902 1 4.48135 1 5.05669Z"
                    stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>

            <p class="text-white text-[15px]">{{ __('Map') }}</p>
        </button>

        <a href="#modalFilterSettings" role="button" data-bs-toggle="modal"
            class="x-fixedParent_trigger bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center justify-center gap-[10px] relative shrink-0 z-[4] grow flex-1">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M16.48 16.5368H19M2.68 10L1 10.044M2.68 10C2.68 11.3255 3.75451 12.4 5.08 12.4C6.40548 12.4 7.48 11.3255 7.48 10C7.48 8.6745 6.40548 7.6 5.08 7.6C3.75451 7.6 2.68 8.6745 2.68 10ZM8.169 10.0441H19M10.801 3.55124H1M19 3.55124H16.48M1 16.5368H10.801M15.88 16.6C15.88 17.9255 14.8055 19 13.48 19C12.1545 19 11.08 17.9255 11.08 16.6C11.08 15.2745 12.1545 14.2 13.48 14.2C14.8055 14.2 15.88 15.2745 15.88 16.6ZM15.88 3.4C15.88 4.72548 14.8055 5.8 13.48 5.8C12.1545 5.8 11.08 4.72548 11.08 3.4C11.08 2.07452 12.1545 1 13.48 1C14.8055 1 15.88 2.07452 15.88 3.4Z"
                    stroke="white" stroke-width="2" stroke-linecap="round" />
            </svg>

            <p class="text-white text-[15px] font-bold">{{ __('Filters') }}</p>

            <p
                class="x-filtersToggled w-[20px] h-[20px] text-center text-white text-[11px] font-bold bg-[#DC6F5A] rounded-[32px] hidden">
            </p>
        </a>

        <div class="absolute bottom-0 left-0 w-full h-[95px]"
            style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
    </div>

</div>

<script>
// Make current filters available to JavaScript
window.currentPageFilters = @json($currentFilters);
window.projectCountUrl = '{{ route('public.ajax.projects.count') }}';
console.log('Current page filters:', window.currentPageFilters);
console.log('Project count URL:', window.projectCountUrl);
</script>
