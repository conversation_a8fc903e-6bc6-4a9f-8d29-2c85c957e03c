<!DOCTYPE html>
<html>
<head>
    <title>Category Filter Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Category Filter Test</h1>
    
    <form id="test-form">
        <h3>Select Categories:</h3>
        <label>
            <input type="checkbox" name="category[]" value="house"> House
        </label><br>
        <label>
            <input type="checkbox" name="category[]" value="apartment"> Apartment
        </label><br>
        <label>
            <input type="checkbox" name="category[]" value="villa"> Villa
        </label><br>
        
        <h3>Other Filters:</h3>
        <label>
            <input type="checkbox" name="no_fee" value="1"> No Fee
        </label><br>
        
        <button type="submit">Test Filter</button>
    </form>
    
    <div id="result">
        <h3>Generated URL:</h3>
        <p id="url-display"></p>
    </div>

    <script>
        function cleanFormData(formDataInput) {
            const formData = formDataInput.filter((item) => item.value !== '' && (item.name !== 'per_page' || (item.name === 'per_page' && parseInt(item.value) !== 12)))

            // Group array fields (like category[]) and handle them properly
            const groupedData = {};
            const queryParts = [];

            formData
                .filter((item) => item.name !== '_token')
                .forEach((item) => {
                    const fieldName = item.name;
                    const fieldValue = item.value;

                    // Check if this is an array field (ends with [])
                    if (fieldName.endsWith('[]')) {
                        const baseName = fieldName.slice(0, -2); // Remove []
                        if (!groupedData[baseName]) {
                            groupedData[baseName] = [];
                        }
                        groupedData[baseName].push(fieldValue);
                    } else {
                        // Regular field
                        queryParts.push(`${encodeURIComponent(fieldName)}=${encodeURIComponent(fieldValue)}`);
                    }
                });

            // Add array fields to query string
            Object.keys(groupedData).forEach((fieldName) => {
                groupedData[fieldName].forEach((value) => {
                    queryParts.push(`${encodeURIComponent(fieldName)}=${encodeURIComponent(value)}`);
                });
            });

            const queryString = queryParts.length > 0 ? `?${queryParts.join('&')}` : '';

            return {
                formData: formData,
                queryString: queryString,
            }
        }

        $('#test-form').on('submit', function(e) {
            e.preventDefault();
            
            const formData = $(this).serializeArray();
            const cleanedData = cleanFormData(formData);
            
            console.log('Form Data:', formData);
            console.log('Cleaned Data:', cleanedData);
            
            $('#url-display').text(window.location.origin + '/' + cleanedData.queryString);
        });
    </script>
</body>
</html>
